package storage

import (
	"encoding/json"
	"fmt"
	"os"

	"VPNCrawler/internal/models"
)

// Storage 数据存储接口
type Storage interface {
	Load() ([]models.VPNInfo, error)
	Save(data []models.VPNInfo) error
	FindNewVPNs(currentVPNs []models.VPNInfo) []models.VPNInfo
	AddNewVPNs(newVPNs []models.VPNInfo) error
}

// FileStorage 文件存储实现
type FileStorage struct {
	filePath string
	vpnData  []models.VPNInfo
}

// NewFileStorage 创建文件存储实例
func NewFileStorage(filePath string) *FileStorage {
	storage := &FileStorage{
		filePath: filePath,
		vpnData:  []models.VPNInfo{},
	}
	// 初始化时加载数据
	data, err := storage.Load()
	if err == nil {
		storage.vpnData = data
	}
	return storage
}

// Load 加载已存储的数据
func (fs *FileStorage) Load() ([]models.VPNInfo, error) {
	if _, err := os.Stat(fs.filePath); os.IsNotExist(err) {
		return []models.VPNInfo{}, nil
	}

	data, err := os.ReadFile(fs.filePath)
	if err != nil {
		return nil, fmt.Errorf("读取数据文件失败: %v", err)
	}

	var vpnData []models.VPNInfo
	if err := json.Unmarshal(data, &vpnData); err != nil {
		return nil, fmt.Errorf("解析数据文件失败: %v", err)
	}

	return vpnData, nil
}

// Save 保存数据到文件
func (fs *FileStorage) Save(data []models.VPNInfo) error {
	jsonData, err := json.MarshalIndent(data, "", "  ")
	if err != nil {
		return fmt.Errorf("序列化数据失败: %v", err)
	}

	if err := os.WriteFile(fs.filePath, jsonData, 0644); err != nil {
		return fmt.Errorf("写入数据文件失败: %v", err)
	}

	// 更新内存中的数据
	fs.vpnData = data
	return nil
}

// FindNewVPNs 查找新的试用机场
func (fs *FileStorage) FindNewVPNs(currentVPNs []models.VPNInfo) []models.VPNInfo {
	var newVPNs []models.VPNInfo

	for _, current := range currentVPNs {
		isNew := true
		for _, existing := range fs.vpnData {
			if existing.Name == current.Name && existing.RegisterURL == current.RegisterURL {
				isNew = false
				break
			}
		}
		if isNew {
			newVPNs = append(newVPNs, current)
		}
	}

	return newVPNs
}

// GetStoredData 获取已存储的数据
func (fs *FileStorage) GetStoredData() []models.VPNInfo {
	return fs.vpnData
}

// AddNewVPNs 添加新的VPN数据
func (fs *FileStorage) AddNewVPNs(newVPNs []models.VPNInfo) error {
	fs.vpnData = append(fs.vpnData, newVPNs...)
	return fs.Save(fs.vpnData)
}
