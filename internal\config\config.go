package config

// NotificationConfig 通知配置
type NotificationConfig struct {
	EnableDingTalk bool   `json:"enable_dingtalk"` // 是否启用钉钉通知
	EnableDesktop  bool   `json:"enable_desktop"`  // 是否启用桌面通知
	EnableFile     bool   `json:"enable_file"`     // 是否启用文件通知
	DingTalkURL    string `json:"dingtalk_url"`    // 钉钉机器人URL
	DingTalkSecret string `json:"dingtalk_secret"` // 钉钉机器人密钥
}

// AppConfig 应用配置
type AppConfig struct {
	DataFile         string             `json:"data_file"`         // 数据存储文件路径
	CheckInterval    int                `json:"check_interval"`    // 检查间隔（分钟）
	NotificationConf NotificationConfig `json:"notification_conf"` // 通知配置
}

// GetDefaultConfig 获取默认配置
func GetDefaultConfig() *AppConfig {
	return &AppConfig{
		DataFile:      "vpn_data.json",
		CheckInterval: 5, // 5分钟
		NotificationConf: NotificationConfig{
			EnableDingTalk: false,
			EnableDesktop:  true,
			EnableFile:     true,
			DingTalkURL:    "",
			DingTalkSecret: "",
		},
	}
}

// GetDemoConfig 获取演示配置（包含钉钉配置示例）
func GetDemoConfig() *AppConfig {
	return &AppConfig{
		DataFile:      "vpn_data.json",
		CheckInterval: 5,
		NotificationConf: NotificationConfig{
			EnableDingTalk: false, // 默认禁用，用户需要手动启用
			EnableDesktop:  true,
			EnableFile:     true,
			DingTalkURL:    "https://oapi.dingtalk.com/robot/send?access_token=7debd389741b51484bf8e10726e397269e9896139496e9cba0f11f06b0cab423",
			DingTalkSecret: "SECdfe010745044c45132014d2f13476cf29792396b572429e73901cd4572c3a63d",
		},
	}
}
