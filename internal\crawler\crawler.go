package crawler

import (
	"fmt"
	"io"
	"net/http"
	"regexp"
	"strings"
	"time"

	"VPNCrawler/internal/models"
)

// WebCrawler 网页爬虫接口
type WebCrawler interface {
	FetchWebPage() (string, error)
	ParseVPNs(html string) ([]models.VPNInfo, error)
}

// VPNWebCrawler VPN网站爬虫实现
type VPNWebCrawler struct {
	baseURL string
	client  *http.Client
}

// NewVPNWebCrawler 创建VPN网站爬虫
func NewVPNWebCrawler() *VPNWebCrawler {
	return &VPNWebCrawler{
		baseURL: "https://ygpy.net/vpn",
		client: &http.Client{
			Timeout: 30 * time.Second,
		},
	}
}

// FetchWebPage 获取网页内容
func (c *VPNWebCrawler) FetchWebPage() (string, error) {
	now := time.Now()
	url := fmt.Sprintf("%s/%d/%02d.html", c.baseURL, now.Year(), int(now.Month()))

	fmt.Printf("正在抓取URL: %s\n", url)

	req, err := http.NewRequest("GET", url, nil)
	if err != nil {
		return "", fmt.Errorf("创建请求失败: %v", err)
	}

	// 设置User-Agent
	req.Header.Set("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36")

	resp, err := c.client.Do(req)
	if err != nil {
		return "", fmt.Errorf("请求失败: %v", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != 200 {
		return "", fmt.Errorf("HTTP状态码: %d", resp.StatusCode)
	}

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return "", fmt.Errorf("读取响应失败: %v", err)
	}

	return string(body), nil
}

// ParseVPNs 解析试用机场信息
func (c *VPNWebCrawler) ParseVPNs(html string) ([]models.VPNInfo, error) {
	var vpnList []models.VPNInfo

	// 使用更通用的正则表达式匹配免费/试用机场的h2标签
	// 支持多种Badge类型：试用机场、免费机场等
	trialRegex := regexp.MustCompile(`<h2[^>]*>\s*([^<]+?)\s*<span[^>]*VPBadge\s+(?:info|tip)[^>]*>\s*<!--\[-->\s*(试用机场|免费机场)\s*<!--\]-->\s*</span>[^<]*<a[^>]*>`)
	trialMatches := trialRegex.FindAllStringSubmatch(html, -1)

	fmt.Printf("找到 %d 个免费/试用机场标题\n", len(trialMatches))

	for i, match := range trialMatches {
		if len(match) < 3 {
			continue
		}

		vpnName := strings.TrimSpace(match[1])
		vpnType := strings.TrimSpace(match[2])
		fmt.Printf("正在解析第 %d 个机场: %s (%s)\n", i+1, vpnName, vpnType)

		// 找到这个机场对应的详细信息区域
		nameIndex := strings.Index(html, match[0])
		if nameIndex == -1 {
			fmt.Printf("未找到机场 %s 的位置索引\n", vpnName)
			continue
		}

		// 查找下一个h2标签的位置作为结束位置
		nextH2Index := strings.Index(html[nameIndex+len(match[0]):], "<h2")
		var sectionHTML string
		if nextH2Index == -1 {
			sectionHTML = html[nameIndex:]
		} else {
			sectionHTML = html[nameIndex : nameIndex+len(match[0])+nextH2Index]
		}

		vpn := models.VPNInfo{
			Name:      vpnName,
			Type:      vpnType,
			AddedTime: time.Now(),
		}

		// 解析各个字段
		c.parseVPNDetails(&vpn, sectionHTML)

		// 验证必要信息
		if vpn.Name != "" && vpn.RegisterURL != "" {
			vpnList = append(vpnList, vpn)
			fmt.Printf("成功解析机场: %s (%s), 注册地址: %s\n", vpn.Name, vpn.Type, vpn.RegisterURL)
		} else {
			fmt.Printf("机场信息不完整，跳过: %s\n", vpn.Name)
		}
	}

	return vpnList, nil
}

// parseVPNDetails 解析VPN详细信息
func (c *VPNWebCrawler) parseVPNDetails(vpn *models.VPNInfo, sectionHTML string) {
	// 提取优惠券代码或获取方式
	vpn.CouponCode = c.extractCouponCode(sectionHTML)

	// 如果仍然没有找到获取方式，根据类型设置默认值
	if vpn.CouponCode == "" {
		if vpn.Type == "免费机场" {
			vpn.CouponCode = "免费注册"
		} else {
			vpn.CouponCode = "试用注册"
		}
	}

	// 提取注册地址（支持中文域名）
	registerPatterns := []string{
		`注册地址：<a[^>]*href="([^"]+)"[^>]*>([^<]+)</a>`,
		`地址：<a[^>]*href="([^"]+)"[^>]*>([^<]+)</a>`,
	}

	for _, pattern := range registerPatterns {
		registerRegex := regexp.MustCompile(pattern)
		if registerMatch := registerRegex.FindStringSubmatch(sectionHTML); len(registerMatch) > 1 {
			vpn.RegisterURL = strings.TrimSpace(registerMatch[1])
			break
		}
	}

	// 提取节点位置
	locationRegex := regexp.MustCompile(`节点位置：([^<\n\r]+?)(?:</p>|<br)`)
	if locationMatch := locationRegex.FindStringSubmatch(sectionHTML); len(locationMatch) > 1 {
		vpn.Locations = strings.TrimSpace(locationMatch[1])
	}

	// 提取协议类型
	protocolRegex := regexp.MustCompile(`协议类型：([^<\n\r]+?)(?:</p>|<br)`)
	if protocolMatch := protocolRegex.FindStringSubmatch(sectionHTML); len(protocolMatch) > 1 {
		vpn.Protocol = strings.TrimSpace(protocolMatch[1])
	}

	// 提取节点数量
	nodeCountRegex := regexp.MustCompile(`节点数量：([^<\n\r]+?)(?:</p>|<br)`)
	if nodeCountMatch := nodeCountRegex.FindStringSubmatch(sectionHTML); len(nodeCountMatch) > 1 {
		vpn.NodeCount = strings.TrimSpace(nodeCountMatch[1])
	}

	// 提取官方群组和频道
	c.extractOfficialGroups(vpn, sectionHTML)

	// 提取更新日期
	updatePatterns := []string{
		`更新于：([^<\n\r]+?)(?:</p>|<br)`,
		`<p>更新于：([^<\n\r]+?)</p>`,
	}

	for _, pattern := range updatePatterns {
		updateRegex := regexp.MustCompile(pattern)
		if updateMatch := updateRegex.FindStringSubmatch(sectionHTML); len(updateMatch) > 1 {
			vpn.UpdateDate = strings.TrimSpace(updateMatch[1])
			break
		}
	}
}

// extractCouponCode 提取优惠券代码或获取方式（优化版本）
func (c *VPNWebCrawler) extractCouponCode(sectionHTML string) string {
	// 方法1: 提取 <code> 标签中的优惠券代码
	couponPatterns := []string{
		`使用(?:优惠券|礼品卡|代码)\s*<code>([^<]+)</code>`,
		`优惠券(?:代码)?[：:]\s*<code>([^<]+)</code>`,
		`礼品卡(?:代码)?[：:]\s*<code>([^<]+)</code>`,
		`代码[：:]\s*<code>([^<]+)</code>`,
		`<code>([A-Z0-9]{4,})</code>`, // 匹配纯大写字母数字组合的代码
	}

	for _, pattern := range couponPatterns {
		couponRegex := regexp.MustCompile(pattern)
		if couponMatch := couponRegex.FindStringSubmatch(sectionHTML); len(couponMatch) > 1 {
			code := strings.TrimSpace(couponMatch[1])
			if code != "" {
				return code
			}
		}
	}

	// 方法2: 提取套餐获取方式
	packagePatterns := []string{
		`注册即送([^。<\n\r]+?)(?:套餐|流量|天)`,
		`免费获得([^。<\n\r]+?)(?:套餐|流量|天)`,
		`赠送([^。<\n\r]+?)(?:套餐|流量|天)`,
		`([0-9]+)\s*元购买[^。<\n\r]*?(?:套餐|流量)`,
		`新用户注册送([^。<\n\r]+?)(?:套餐|流量|天)`,
		`首次注册送([^。<\n\r]+?)(?:套餐|流量|天)`,
	}

	for _, pattern := range packagePatterns {
		packageRegex := regexp.MustCompile(pattern)
		if packageMatch := packageRegex.FindStringSubmatch(sectionHTML); len(packageMatch) > 1 {
			if strings.Contains(packageMatch[0], "0 元购买") ||
				strings.Contains(packageMatch[0], "注册即送") ||
				strings.Contains(packageMatch[0], "免费获得") ||
				strings.Contains(packageMatch[0], "赠送") {
				return "免费获取"
			} else {
				return "需购买"
			}
		}
	}

	// 方法3: 查找节点位置上一个<p>标签的内容
	locationIndex := strings.Index(sectionHTML, "节点位置：")
	if locationIndex != -1 {
		beforeLocation := sectionHTML[:locationIndex]
		pTagRegex := regexp.MustCompile(`<p[^>]*>([^<]*(?:<(?!/?p)[^<]*)*)</p>`)
		pMatches := pTagRegex.FindAllStringSubmatch(beforeLocation, -1)

		if len(pMatches) > 0 {
			lastPContent := strings.TrimSpace(pMatches[len(pMatches)-1][1])
			couponKeywords := []string{"优惠券", "礼品卡", "代码", "免费", "试用", "注册送", "赠送"}
			for _, keyword := range couponKeywords {
				if strings.Contains(lastPContent, keyword) {
					cleanContent := regexp.MustCompile(`<[^>]*>`).ReplaceAllString(lastPContent, "")
					cleanContent = regexp.MustCompile(`\s+`).ReplaceAllString(cleanContent, " ")
					cleanContent = strings.TrimSpace(cleanContent)

					if len(cleanContent) > 0 && len(cleanContent) < 100 {
						return cleanContent
					}
				}
			}
		}
	}

	// 方法4: 通用文本模式匹配
	generalPatterns := []string{
		`免费注册即可使用`,
		`注册即可免费试用`,
		`新用户免费试用`,
		`免费试用\d+天`,
		`试用期\d+天`,
		`无需优惠券`,
		`直接注册使用`,
	}

	for _, pattern := range generalPatterns {
		generalRegex := regexp.MustCompile(pattern)
		if generalRegex.MatchString(sectionHTML) {
			return "免费试用"
		}
	}

	// 方法5: 根据机场类型设置默认值
	if strings.Contains(sectionHTML, "免费机场") {
		return "免费注册"
	} else if strings.Contains(sectionHTML, "试用机场") {
		return "试用注册"
	}

	return ""
}

// extractOfficialGroups 提取官方群组和频道信息
func (c *VPNWebCrawler) extractOfficialGroups(vpn *models.VPNInfo, sectionHTML string) {
	// 提取官方群组（处理"无"的情况）
	groupRegex := regexp.MustCompile(`官方群组：(?:<a[^>]*href="([^"]+)"[^>]*>([^<]+)</a>|([^<\n\r]+?))(?:</p>|<br)`)
	if groupMatch := groupRegex.FindStringSubmatch(sectionHTML); len(groupMatch) > 1 {
		if groupMatch[1] != "" {
			// 有链接的情况
			vpn.OfficialGroup = strings.TrimSpace(groupMatch[1])
		} else if groupMatch[3] != "" && !strings.Contains(groupMatch[3], "无") {
			// 纯文本但不是"无"
			vpn.OfficialGroup = strings.TrimSpace(groupMatch[3])
		}
	}

	// 提取官方频道（处理"无"的情况）
	channelRegex := regexp.MustCompile(`官方频道：(?:<a[^>]*href="([^"]+)"[^>]*>([^<]+)</a>|([^<\n\r]+?))(?:</p>|<br)`)
	if channelMatch := channelRegex.FindStringSubmatch(sectionHTML); len(channelMatch) > 1 {
		var channelInfo string
		if channelMatch[1] != "" {
			// 有链接的情况
			channelInfo = "频道: " + strings.TrimSpace(channelMatch[1])
		} else if channelMatch[3] != "" && !strings.Contains(channelMatch[3], "无") {
			// 纯文本但不是"无"
			channelInfo = "频道: " + strings.TrimSpace(channelMatch[3])
		}

		if channelInfo != "" {
			if vpn.OfficialGroup != "" {
				vpn.OfficialGroup += " | " + channelInfo
			} else {
				vpn.OfficialGroup = channelInfo
			}
		}
	}
}
