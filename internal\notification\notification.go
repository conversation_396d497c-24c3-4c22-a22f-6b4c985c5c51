package notification

import (
	"crypto/hmac"
	"crypto/sha256"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"os"
	"os/exec"
	"strings"
	"time"

	"github.com/gen2brain/beeep"

	"VPNCrawler/internal/config"
	"VPNCrawler/internal/models"
)

// Notifier 通知器接口
type Notifier interface {
	SendNotification(vpns []models.VPNInfo) error
}

// NotificationManager 通知管理器
type NotificationManager struct {
	config    config.NotificationConfig
	notifiers []Notifier
}

// NewNotificationManager 创建通知管理器
func NewNotificationManager(conf config.NotificationConfig) *NotificationManager {
	manager := &NotificationManager{
		config:    conf,
		notifiers: []Notifier{},
	}

	// 根据配置添加通知器
	if conf.EnableDingTalk && conf.DingTalkURL != "" && conf.DingTalkSecret != "" {
		manager.notifiers = append(manager.notifiers, NewDingTalkNotifier(conf.DingTalkURL, conf.DingTalkSecret))
	}

	if conf.EnableDesktop {
		manager.notifiers = append(manager.notifiers, NewDesktopNotifier())
	}

	if conf.EnableFile {
		manager.notifiers = append(manager.notifiers, NewFileNotifier())
	}

	return manager
}

// SendNotifications 发送通知（支持多种方式）
func (nm *NotificationManager) SendNotifications(vpns []models.VPNInfo) {
	if len(vpns) == 0 {
		return
	}

	var errors []string

	for _, notifier := range nm.notifiers {
		if err := notifier.SendNotification(vpns); err != nil {
			errors = append(errors, err.Error())
		}
	}

	// 输出错误信息
	if len(errors) > 0 {
		fmt.Println("通知发送过程中出现以下错误:")
		for _, errMsg := range errors {
			fmt.Printf("- %s\n", errMsg)
		}
	}

	// 如果没有启用任何通知方式，给出提示
	if len(nm.notifiers) == 0 {
		fmt.Println("提示: 当前未启用任何通知方式，可以在配置中启用钉钉通知、桌面通知或文件通知")
	}
}

// DingTalkNotifier 钉钉通知器
type DingTalkNotifier struct {
	webhookURL string
	secret     string
}

// NewDingTalkNotifier 创建钉钉通知器
func NewDingTalkNotifier(webhookURL, secret string) *DingTalkNotifier {
	return &DingTalkNotifier{
		webhookURL: webhookURL,
		secret:     secret,
	}
}

// SendNotification 发送钉钉通知
func (dn *DingTalkNotifier) SendNotification(vpns []models.VPNInfo) error {
	if len(vpns) == 0 {
		return nil
	}

	// 构建Markdown格式的消息
	var msgBuilder strings.Builder
	msgBuilder.WriteString("# 🎉 发现新的免费/试用机场!\n\n")

	for i, vpn := range vpns {
		msgBuilder.WriteString(fmt.Sprintf("## %d. %s (%s)\n\n", i+1, vpn.Name, vpn.Type))

		if vpn.CouponCode != "" {
			if vpn.Package != "" {
				msgBuilder.WriteString(fmt.Sprintf("**获取方式**: %s (%s)\n\n", vpn.CouponCode, vpn.Package))
			} else {
				msgBuilder.WriteString(fmt.Sprintf("**获取方式**: %s\n\n", vpn.CouponCode))
			}
		}

		if vpn.RegisterURL != "" {
			msgBuilder.WriteString(fmt.Sprintf("**注册地址**: [点击注册](%s)\n\n", vpn.RegisterURL))
		}

		if vpn.Locations != "" {
			msgBuilder.WriteString(fmt.Sprintf("**节点位置**: %s\n\n", vpn.Locations))
		}

		if vpn.Protocol != "" {
			msgBuilder.WriteString(fmt.Sprintf("**协议类型**: %s\n\n", vpn.Protocol))
		}

		if vpn.NodeCount != "" {
			msgBuilder.WriteString(fmt.Sprintf("**节点数量**: %s\n\n", vpn.NodeCount))
		}

		if vpn.OfficialGroup != "" {
			msgBuilder.WriteString(fmt.Sprintf("**官方群组**: [点击加入](%s)\n\n", vpn.OfficialGroup))
		}

		if vpn.UpdateDate != "" {
			msgBuilder.WriteString(fmt.Sprintf("**更新日期**: %s\n\n", vpn.UpdateDate))
		}

		msgBuilder.WriteString("---\n\n")
	}

	message := models.DingTalkMessage{
		MsgType: "markdown",
		Markdown: models.DingTalkMarkdown{
			Title: fmt.Sprintf("发现 %d 个新的免费/试用机场", len(vpns)),
			Text:  msgBuilder.String(),
		},
	}

	// 生成签名
	timestamp, sign := dn.generateSign()

	// 构建完整的URL
	fullURL := fmt.Sprintf("%s&timestamp=%d&sign=%s", dn.webhookURL, timestamp, sign)

	// 序列化消息
	jsonData, err := json.Marshal(message)
	if err != nil {
		return fmt.Errorf("序列化钉钉消息失败: %v", err)
	}

	// 发送HTTP请求
	resp, err := http.Post(fullURL, "application/json", strings.NewReader(string(jsonData)))
	if err != nil {
		return fmt.Errorf("发送钉钉消息失败: %v", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != 200 {
		body, _ := io.ReadAll(resp.Body)
		return fmt.Errorf("钉钉API返回错误状态码 %d: %s", resp.StatusCode, string(body))
	}

	fmt.Printf("成功发送钉钉通知，包含 %d 个新机场\n", len(vpns))
	return nil
}

// generateSign 生成钉钉签名
func (dn *DingTalkNotifier) generateSign() (int64, string) {
	timestamp := time.Now().UnixNano() / 1e6
	stringToSign := fmt.Sprintf("%d\n%s", timestamp, dn.secret)

	h := hmac.New(sha256.New, []byte(dn.secret))
	h.Write([]byte(stringToSign))
	signature := base64.StdEncoding.EncodeToString(h.Sum(nil))

	return timestamp, signature
}

// DesktopNotifier 桌面通知器
type DesktopNotifier struct{}

// NewDesktopNotifier 创建桌面通知器
func NewDesktopNotifier() *DesktopNotifier {
	return &DesktopNotifier{}
}

// SendNotification 发送桌面通知
func (dn *DesktopNotifier) SendNotification(vpns []models.VPNInfo) error {
	if len(vpns) == 0 {
		return nil
	}

	title := fmt.Sprintf("发现 %d 个新的免费/试用机场!", len(vpns))

	var message strings.Builder
	for i, vpn := range vpns {
		if i >= 3 { // 桌面通知只显示前3个，避免内容过长
			message.WriteString(fmt.Sprintf("...还有 %d 个机场", len(vpns)-3))
			break
		}

		message.WriteString(fmt.Sprintf("%d. %s (%s)", i+1, vpn.Name, vpn.Type))
		if vpn.CouponCode != "" {
			message.WriteString(fmt.Sprintf(" - %s", vpn.CouponCode))
		}
		if i < len(vpns)-1 && i < 2 {
			message.WriteString("\n")
		}
	}

	// 尝试发送桌面通知，使用多种方式
	var lastErr error

	// 方法1: 使用默认通知
	lastErr = beeep.Notify(title, message.String(), "")
	if lastErr == nil {
		fmt.Printf("成功发送桌面通知，包含 %d 个新机场\n", len(vpns))
		return nil
	}

	// 方法2: 尝试使用Alert（简单弹窗）
	lastErr = beeep.Alert(title, message.String(), "")
	if lastErr == nil {
		fmt.Printf("成功发送桌面弹窗通知，包含 %d 个新机场\n", len(vpns))
		return nil
	}

	// 方法3: 使用Beep（系统提示音）+ 控制台输出
	beeep.Beep(beeep.DefaultFreq, beeep.DefaultDuration)
	fmt.Printf("桌面通知发送失败，已播放系统提示音。发现 %d 个新机场\n", len(vpns))

	// 在控制台显示详细信息作为备选方案
	fmt.Println("\n=== 桌面通知内容 ===")
	fmt.Printf("标题: %s\n", title)
	fmt.Printf("内容: %s\n", message.String())
	fmt.Println("==================")

	// 不返回错误，因为我们已经通过其他方式通知了用户
	return nil
}

// FileNotifier 文件通知器
type FileNotifier struct{}

// NewFileNotifier 创建文件通知器
func NewFileNotifier() *FileNotifier {
	return &FileNotifier{}
}

// SendNotification 发送文件通知
func (fn *FileNotifier) SendNotification(vpns []models.VPNInfo) error {
	if len(vpns) == 0 {
		return nil
	}

	// 生成文件名：格式为 "VPN通知_2024-01-15_14-30-25.txt"
	now := time.Now()
	filename := fmt.Sprintf("VPN通知_%s.txt", now.Format("2006-01-02_15-04-05"))

	// 构建文件内容
	var content strings.Builder
	content.WriteString(fmt.Sprintf("=== 发现 %d 个新的免费/试用机场 ===\n", len(vpns)))
	content.WriteString(fmt.Sprintf("发现时间: %s\n\n", now.Format("2006-01-02 15:04:05")))

	for i, vpn := range vpns {
		content.WriteString(fmt.Sprintf("%d. %s (%s)\n", i+1, vpn.Name, vpn.Type))
		content.WriteString(strings.Repeat("=", 50) + "\n")

		if vpn.CouponCode != "" {
			if vpn.Package != "" {
				content.WriteString(fmt.Sprintf("获取方式: %s (%s)\n", vpn.CouponCode, vpn.Package))
			} else {
				content.WriteString(fmt.Sprintf("获取方式: %s\n", vpn.CouponCode))
			}
		}

		if vpn.RegisterURL != "" {
			content.WriteString(fmt.Sprintf("注册地址: %s\n", vpn.RegisterURL))
		}

		if vpn.Locations != "" {
			content.WriteString(fmt.Sprintf("节点位置: %s\n", vpn.Locations))
		}

		if vpn.Protocol != "" {
			content.WriteString(fmt.Sprintf("协议类型: %s\n", vpn.Protocol))
		}

		if vpn.NodeCount != "" {
			content.WriteString(fmt.Sprintf("节点数量: %s\n", vpn.NodeCount))
		}

		if vpn.OfficialGroup != "" {
			content.WriteString(fmt.Sprintf("官方群组: %s\n", vpn.OfficialGroup))
		}

		if vpn.UpdateDate != "" {
			content.WriteString(fmt.Sprintf("更新日期: %s\n", vpn.UpdateDate))
		}

		content.WriteString("\n")
	}

	content.WriteString("\n=== 使用说明 ===\n")
	content.WriteString("1. 点击注册地址进行注册\n")
	content.WriteString("2. 根据获取方式获取试用套餐\n")
	content.WriteString("3. 如有优惠券代码，请在注册时使用\n")
	content.WriteString("4. 建议优先选择节点较多、协议支持较好的机场\n")

	// 写入文件
	err := os.WriteFile(filename, []byte(content.String()), 0644)
	if err != nil {
		return fmt.Errorf("写入通知文件失败: %v", err)
	}

	fmt.Printf("成功创建通知文件: %s\n", filename)

	// 自动打开文件
	err = fn.openFile(filename)
	if err != nil {
		fmt.Printf("自动打开文件失败: %v\n", err)
		fmt.Printf("请手动打开文件: %s\n", filename)
	} else {
		fmt.Printf("已自动打开通知文件\n")
	}

	return nil
}

// openFile 根据操作系统自动打开文件
func (fn *FileNotifier) openFile(filename string) error {
	var cmd *exec.Cmd
	// Windows系统使用start命令
	cmd = exec.Command("cmd", "/c", "start", "", filename)
	return cmd.Start()
}
