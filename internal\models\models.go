package models

import "time"

// VPNInfo 试用机场信息结构体
type VPNInfo struct {
	Name          string    `json:"name"`           // 机场名称
	Type          string    `json:"type"`           // 机场类型（试用机场、免费机场等）
	CouponCode    string    `json:"coupon_code"`    // 优惠券代码或获取方式
	RegisterURL   string    `json:"register_url"`   // 注册地址
	UpdateDate    string    `json:"update_date"`    // 更新日期
	Locations     string    `json:"locations"`      // 节点位置
	Protocol      string    `json:"protocol"`       // 协议类型
	NodeCount     string    `json:"node_count"`     // 节点数量
	OfficialGroup string    `json:"official_group"` // 官方群组/频道
	Package       string    `json:"package"`        // 套餐信息
	AddedTime     time.Time `json:"added_time"`     // 添加时间
}

// DingTalkMessage 钉钉消息结构体
type DingTalkMessage struct {
	MsgType  string           `json:"msgtype"`
	Markdown DingTalkMarkdown `json:"markdown"`
}

// DingTalkMarkdown 钉钉Markdown消息结构体
type DingTalkMarkdown struct {
	Title string `json:"title"`
	Text  string `json:"text"`
}
