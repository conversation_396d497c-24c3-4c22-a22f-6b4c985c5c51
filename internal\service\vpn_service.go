package service

import (
	"fmt"
	"time"

	"VPNCrawler/internal/config"
	"VPNCrawler/internal/crawler"
	"VPNCrawler/internal/models"
	"VPNCrawler/internal/notification"
	"VPNCrawler/internal/storage"
)

// VPNService VPN服务
type VPNService struct {
	config           *config.AppConfig
	crawler          crawler.WebCrawler
	storage          storage.Storage
	notificationMgr  *notification.NotificationManager
}

// NewVPNService 创建VPN服务实例
func NewVPNService(conf *config.AppConfig) *VPNService {
	return &VPNService{
		config:          conf,
		crawler:         crawler.NewVPNWebCrawler(),
		storage:         storage.NewFileStorage(conf.DataFile),
		notificationMgr: notification.NewNotificationManager(conf.NotificationConf),
	}
}

// CrawlAndCheck 执行爬取和检查
func (s *VPNService) CrawlAndCheck() {
	fmt.Printf("\n=== %s 开始爬取试用机场信息 ===\n", time.Now().Format("2006-01-02 15:04:05"))

	// 获取网页内容
	html, err := s.crawler.FetchWebPage()
	if err != nil {
		fmt.Printf("获取网页失败: %v\n", err)
		return
	}

	fmt.Printf("网页内容长度: %d 字符\n", len(html))

	// 解析试用机场信息
	currentVPNs, err := s.crawler.ParseVPNs(html)
	if err != nil {
		fmt.Printf("解析网页失败: %v\n", err)
		return
	}

	fmt.Printf("本次爬取到 %d 个试用机场\n", len(currentVPNs))

	// 打印当前爬取到的机场信息
	if len(currentVPNs) > 0 {
		fmt.Println("\n当前爬取到的机场:")
		for i, vpn := range currentVPNs {
			fmt.Printf("%d. %s - %s\n", i+1, vpn.Name, vpn.RegisterURL)
		}
	}

	// 查找新的机场
	newVPNs := s.storage.FindNewVPNs(currentVPNs)

	if len(newVPNs) > 0 {
		fmt.Printf("\n🎉 发现 %d 个新的试用机场:\n", len(newVPNs))
		for i, vpn := range newVPNs {
			fmt.Printf("%d. %s\n", i+1, vpn.Name)
		}

		// 发送通知（支持多种方式）
		s.notificationMgr.SendNotifications(newVPNs)

		// 更新本地数据
		if err := s.storage.AddNewVPNs(newVPNs); err != nil {
			fmt.Printf("保存数据失败: %v\n", err)
		} else {
			fmt.Println("数据已保存到本地文件")
		}
	} else {
		fmt.Println("没有发现新的试用机场")
	}

	// 获取总数据量
	totalCount := len(s.getStoredVPNs())
	fmt.Printf("=== 本次检查完成，总共存储 %d 个机场 ===\n\n", totalCount)
}

// Run 启动定时任务
func (s *VPNService) Run() {
	// 立即执行一次
	s.CrawlAndCheck()

	// 根据配置设置检查间隔
	interval := time.Duration(s.config.CheckInterval) * time.Minute
	ticker := time.NewTicker(interval)
	defer ticker.Stop()

	fmt.Printf("定时任务已启动，每%d分钟检查一次...\n", s.config.CheckInterval)

	for range ticker.C {
		s.CrawlAndCheck()
	}
}

// getStoredVPNs 获取已存储的VPN数据
func (s *VPNService) getStoredVPNs() []models.VPNInfo {
	if fileStorage, ok := s.storage.(*storage.FileStorage); ok {
		return fileStorage.GetStoredData()
	}
	// 如果不是FileStorage，尝试重新加载
	data, err := s.storage.Load()
	if err != nil {
		fmt.Printf("获取存储数据失败: %v\n", err)
		return []models.VPNInfo{}
	}
	return data
}

// PrintConfig 打印当前配置信息
func (s *VPNService) PrintConfig() {
	fmt.Println("=== 应用配置 ===")
	fmt.Printf("数据文件: %s\n", s.config.DataFile)
	fmt.Printf("检查间隔: %d 分钟\n", s.config.CheckInterval)
	
	fmt.Println("\n=== 通知配置 ===")
	if s.config.NotificationConf.EnableDingTalk {
		fmt.Println("✓ 钉钉通知: 已启用")
	} else {
		fmt.Println("✗ 钉钉通知: 已禁用")
	}

	if s.config.NotificationConf.EnableDesktop {
		fmt.Println("✓ 桌面通知: 已启用")
	} else {
		fmt.Println("✗ 桌面通知: 已禁用")
	}

	if s.config.NotificationConf.EnableFile {
		fmt.Println("✓ 文件通知: 已启用")
	} else {
		fmt.Println("✗ 文件通知: 已禁用")
	}
	fmt.Println()

	// 检查钉钉配置（如果启用了钉钉通知）
	if s.config.NotificationConf.EnableDingTalk {
		if s.config.NotificationConf.DingTalkURL == "" || s.config.NotificationConf.DingTalkSecret == "" {
			fmt.Println("警告: 钉钉通知已启用但配置不完整！")
			fmt.Println("请在配置中设置以下参数：")
			fmt.Println("- DingTalkURL: 钉钉机器人的 webhook URL")
			fmt.Println("- DingTalkSecret: 钉钉机器人的加签密钥")
			fmt.Println("或者将 EnableDingTalk 设置为 false 禁用钉钉通知")
			fmt.Println()
		}
	}
}
