# VPNCrawler - 代码结构优化版本

## 项目简介

VPNCrawler 是一个用于爬取试用机场信息的工具，支持多种通知方式（钉钉、桌面通知、文件通知）。

## 代码结构

经过模块化重构后，项目采用了清晰的分层架构：

```
VPNCrawler/
├── main.go                    # 主程序入口
├── systray_windows.go         # 系统托盘功能
├── embed.go                   # 静态资源嵌入
├── internal/                  # 内部模块
│   ├── models/               # 数据模型
│   │   └── models.go         # VPN信息、钉钉消息等数据结构
│   ├── config/               # 配置管理
│   │   └── config.go         # 应用配置和通知配置
│   ├── storage/              # 数据存储
│   │   └── storage.go        # 文件存储实现和接口定义
│   ├── crawler/              # 网页爬取
│   │   └── crawler.go        # 网页获取和解析逻辑
│   ├── notification/         # 通知系统
│   │   └── notification.go   # 钉钉、桌面、文件通知实现
│   └── service/              # 业务服务
│       └── vpn_service.go    # 主要业务逻辑协调
└── static/                   # 静态资源
    └── favicon.ico
```

## 模块说明

### 1. models 模块
- **VPNInfo**: 试用机场信息结构体
- **DingTalkMessage**: 钉钉消息结构体
- **DingTalkMarkdown**: 钉钉Markdown消息结构体

### 2. config 模块
- **NotificationConfig**: 通知配置结构体
- **AppConfig**: 应用配置结构体
- **GetDefaultConfig()**: 获取默认配置
- **GetDemoConfig()**: 获取演示配置（包含钉钉配置示例）

### 3. storage 模块
- **Storage**: 数据存储接口
- **FileStorage**: 文件存储实现
- 支持数据加载、保存、查找新VPN等功能

### 4. crawler 模块
- **WebCrawler**: 网页爬虫接口
- **VPNWebCrawler**: VPN网站爬虫实现
- 负责网页获取和VPN信息解析

### 5. notification 模块
- **Notifier**: 通知器接口
- **NotificationManager**: 通知管理器
- **DingTalkNotifier**: 钉钉通知器
- **DesktopNotifier**: 桌面通知器
- **FileNotifier**: 文件通知器

### 6. service 模块
- **VPNService**: VPN服务主类
- 协调各个模块，实现完整的业务流程

## 优化亮点

### 1. 模块化设计
- 按功能将代码拆分为独立模块
- 每个模块职责单一，便于维护和测试
- 使用接口定义，支持不同实现

### 2. 配置管理
- 集中管理所有配置项
- 提供默认配置和演示配置
- 配置验证和错误提示

### 3. 通知系统
- 支持多种通知方式
- 可灵活启用/禁用不同通知类型
- 统一的通知管理器

### 4. 存储抽象
- 定义存储接口，支持扩展
- 当前实现文件存储，可扩展数据库存储
- 数据操作封装良好

### 5. 错误处理
- 完善的错误处理机制
- 详细的日志输出
- 优雅的降级处理

## 使用方法

### 基本使用
```go
// 获取配置
appConfig := config.GetDemoConfig()

// 创建服务
vpnService := service.NewVPNService(appConfig)

// 启动服务
vpnService.Run()
```

### 自定义配置
```go
// 创建自定义配置
appConfig := &config.AppConfig{
    DataFile:      "my_vpn_data.json",
    CheckInterval: 10, // 10分钟检查一次
    NotificationConf: config.NotificationConfig{
        EnableDingTalk: true,
        EnableDesktop:  true,
        EnableFile:     false,
        DingTalkURL:    "your_webhook_url",
        DingTalkSecret: "your_secret",
    },
}
```

## 编译和运行

```bash
# 编译
go build -v

# 运行
./VPNCrawler.exe
```

## 扩展性

新的模块化架构使得扩展变得非常容易：

1. **添加新的通知方式**: 实现 `Notifier` 接口
2. **添加新的存储方式**: 实现 `Storage` 接口  
3. **添加新的爬虫**: 实现 `WebCrawler` 接口
4. **修改配置**: 在 `config` 模块中添加新的配置项

## 代码质量提升

- **可读性**: 代码结构清晰，职责分明
- **可维护性**: 模块化设计，便于修改和扩展
- **可测试性**: 接口抽象，便于单元测试
- **可扩展性**: 插件化架构，支持功能扩展
